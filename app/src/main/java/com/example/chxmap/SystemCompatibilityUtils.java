package com.example.chxmap;

import android.content.Context;
import android.os.Build;
import android.util.Log;

/**
 * 系统兼容性工具类
 * 处理各种设备厂商的兼容性问题和错误过滤
 */
public class SystemCompatibilityUtils {
    
    private static final String TAG = "SystemCompatibility";
    
    // 需要过滤的错误关键词
    private static final String[] FILTERED_ERROR_KEYWORDS = {
        "AtomicFileUtils",
        "HW_EXTFLAG_XLAYOUT_ENABLED", 
        "HwApkAssets",
        "easygo.json",
        "ReflectUtils",
        "AwareLog"
    };
    
    // 华为设备标识
    private static final String[] HUAWEI_MANUFACTURERS = {
        "huawei", "honor", "hw"
    };
    
    /**
     * 检查是否为华为设备
     */
    public static boolean isHuaweiDevice() {
        String manufacturer = Build.MANUFACTURER.toLowerCase();
        String brand = Build.BRAND.toLowerCase();
        
        for (String huaweiId : HUAWEI_MANUFACTURERS) {
            if (manufacturer.contains(huaweiId) || brand.contains(huaweiId)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 过滤系统错误日志
     * 判断是否为可忽略的系统错误
     */
    public static boolean shouldFilterError(String errorMessage) {
        if (errorMessage == null || errorMessage.isEmpty()) {
            return false;
        }
        
        String lowerMessage = errorMessage.toLowerCase();
        for (String keyword : FILTERED_ERROR_KEYWORDS) {
            if (lowerMessage.contains(keyword.toLowerCase())) {
                Log.d(TAG, "过滤系统错误: " + keyword);
                return true;
            }
        }
        return false;
    }
    
    /**
     * 安全的日志输出
     * 过滤掉已知的系统错误
     */
    public static void safeLog(int priority, String tag, String message) {
        if (!shouldFilterError(message)) {
            Log.println(priority, tag, message);
        }
    }
    
    /**
     * 初始化华为设备兼容性
     */
    public static void initHuaweiCompatibility(Context context) {
        if (!isHuaweiDevice()) {
            return;
        }
        
        try {
            Log.d(TAG, "初始化华为设备兼容性...");
            
            // 创建华为系统需要的文件和目录
            createHuaweiRequiredFiles(context);
            
            // 设置华为系统属性
            setHuaweiSystemProperties();
            
            // 禁用华为优化功能
            disableHuaweiOptimizations();
            
            Log.d(TAG, "华为设备兼容性初始化完成");
            
        } catch (Exception e) {
            Log.e(TAG, "初始化华为兼容性失败", e);
        }
    }
    
    /**
     * 创建华为系统需要的文件
     */
    private static void createHuaweiRequiredFiles(Context context) {
        try {
            // 创建easygo.json文件
            java.io.File easygoFile = new java.io.File(context.getFilesDir(), "easygo.json");
            if (!easygoFile.exists()) {
                try (java.io.FileWriter writer = new java.io.FileWriter(easygoFile)) {
                    writer.write("{\n");
                    writer.write("  \"version\": \"1.0\",\n");
                    writer.write("  \"compatible\": true,\n");
                    writer.write("  \"huawei_optimized\": false,\n");
                    writer.write("  \"webview_compatible\": true\n");
                    writer.write("}");
                    Log.d(TAG, "创建easygo.json文件成功");
                }
            }
            
            // 创建华为配置目录
            java.io.File hwDir = new java.io.File(context.getFilesDir(), "huawei");
            if (!hwDir.exists()) {
                hwDir.mkdirs();
                Log.d(TAG, "创建华为配置目录成功");
            }
            
            // 创建原子文件目录
            java.io.File atomicDir = new java.io.File(context.getCacheDir(), "atomic");
            if (!atomicDir.exists()) {
                atomicDir.mkdirs();
                Log.d(TAG, "创建原子文件目录成功");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "创建华为系统文件失败", e);
        }
    }
    
    /**
     * 设置华为系统属性
     */
    private static void setHuaweiSystemProperties() {
        try {
            // 通过反射设置系统属性
            Class<?> systemPropertiesClass = Class.forName("android.os.SystemProperties");
            java.lang.reflect.Method setMethod = systemPropertiesClass.getMethod("set", String.class, String.class);
            
            // 禁用华为布局优化
            setMethod.invoke(null, "ro.config.hw_optb", "0");
            setMethod.invoke(null, "ro.config.hw_xlayout", "0");
            
            // 禁用华为性能优化
            setMethod.invoke(null, "ro.config.hw_perfhub", "0");
            setMethod.invoke(null, "ro.config.hw_perf_opt", "0");
            
            // 设置WebView兼容模式
            setMethod.invoke(null, "webview.hw_accel.disabled", "1");
            
            Log.d(TAG, "华为系统属性设置完成");
            
        } catch (Exception e) {
            Log.w(TAG, "设置华为系统属性失败: " + e.getMessage());
        }
    }
    
    /**
     * 禁用华为优化功能
     */
    private static void disableHuaweiOptimizations() {
        try {
            // 设置JVM参数禁用华为优化
            System.setProperty("huawei.optimization.disabled", "true");
            System.setProperty("hw.extflag.xlayout.enabled", "false");
            System.setProperty("hw.apk.assets.enabled", "false");
            
            Log.d(TAG, "华为优化功能已禁用");
            
        } catch (Exception e) {
            Log.e(TAG, "禁用华为优化失败", e);
        }
    }
    
    /**
     * 处理WebView兼容性
     */
    public static void handleWebViewCompatibility(Context context) {
        try {
            Log.d(TAG, "处理WebView兼容性...");
            
            // 设置WebView数据目录
            java.io.File webViewDir = new java.io.File(context.getApplicationInfo().dataDir, "app_webview");
            if (!webViewDir.exists()) {
                webViewDir.mkdirs();
            }
            
            // 设置WebView缓存目录
            java.io.File webViewCacheDir = new java.io.File(context.getCacheDir(), "webview");
            if (!webViewCacheDir.exists()) {
                webViewCacheDir.mkdirs();
            }
            
            // 创建WebView数据库目录
            java.io.File webViewDbDir = new java.io.File(webViewDir, "databases");
            if (!webViewDbDir.exists()) {
                webViewDbDir.mkdirs();
            }
            
            Log.d(TAG, "WebView兼容性处理完成");
            
        } catch (Exception e) {
            Log.e(TAG, "处理WebView兼容性失败", e);
        }
    }
    
    /**
     * 获取设备信息用于调试
     */
    public static String getDeviceInfo() {
        StringBuilder info = new StringBuilder();
        info.append("设备制造商: ").append(Build.MANUFACTURER).append("\n");
        info.append("设备品牌: ").append(Build.BRAND).append("\n");
        info.append("设备型号: ").append(Build.MODEL).append("\n");
        info.append("Android版本: ").append(Build.VERSION.RELEASE).append("\n");
        info.append("API级别: ").append(Build.VERSION.SDK_INT).append("\n");
        info.append("是否华为设备: ").append(isHuaweiDevice()).append("\n");
        return info.toString();
    }
    
    /**
     * 记录设备信息
     */
    public static void logDeviceInfo() {
        Log.d(TAG, "设备信息:\n" + getDeviceInfo());
    }
}
